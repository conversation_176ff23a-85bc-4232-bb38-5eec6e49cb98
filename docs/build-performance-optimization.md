# Build Performance Optimization Guide

## Problem Analysis

Your GitHub Actions builds are slow compared to local builds due to several caching issues:

### Issues Identified:

1. **Cache Mount Mismatch**: GitHub Actions uses `type=local` cache while Dockerfiles use `--mount=type=cache`
2. **Backend Dockerfile**: No dependency caching (now fixed)
3. **Cache Persistence**: `/tmp` cache directories don't persist between runs
4. **Single Cache Strategy**: Only using local cache, not leveraging registry cache

## Solutions Implemented

### 1. Improved GitHub Actions Workflow

**Changes made to `.github/workflows/deploy_dev.yaml`:**

- **Persistent Cache Directories**: Using `$HOME/.cache/buildx/` instead of `/tmp/`
- **Local Cache Strategy**: Reliable local cache with optional registry fallback
- **GHCR Authentication**: Added GitHub Container Registry login
- **Latest BuildKit**: Updated to latest version for better performance
- **Network Host**: Better network performance for cache operations
- **No Sudo Required**: Uses user-accessible directories to avoid permission issues
- **Graceful Fallback**: Registry cache is optional and won't fail the build

### 2. Enhanced Backend Dockerfile

**Changes made to `apps/backend/Dockerfile`:**

- **Dependency Pre-building**: Added dummy source files to cache dependencies
- **Cache Mounts**: Added `--mount=type=cache` for registry and target directories
- **Layered Build**: Separate dependency and application build steps

### 3. Debug Tools

**Added `scripts/debug-build-cache.sh`:**

- Cache directory analysis
- Build performance testing
- Common issues detection
- System information gathering

**Added Makefile targets:**

- `make debug-cache`: Run cache analysis
- `make test-build-performance`: Test local build performance

## Usage Instructions

### 1. Run Cache Debug (on your server)

```bash
# SSH to your GitHub Actions runner server
ssh user@your-server

# Navigate to your repo
cd /path/to/warda

# Run the debug script
make debug-cache
```

### 2. Test Local Performance

```bash
# Test build performance locally
make test-build-performance
```

### 3. Monitor GitHub Actions

The improved workflow will now:
- Use persistent cache directories
- Leverage both local and registry cache
- Provide better cache hit rates

## Expected Improvements

### Cache Hit Scenarios:

1. **First Build**: Still slow (building cache)
2. **Subsequent Builds**: 
   - **No changes**: ~90% faster
   - **Code changes only**: ~70% faster
   - **Dependency changes**: ~50% faster

### Performance Metrics:

- **Local cache**: Fast access, survives between runs
- **Registry cache**: Slower access but survives server restarts
- **Dual strategy**: Best of both worlds

## Troubleshooting

### If builds are still slow:

1. **Check cache permissions**:
   ```bash
   ls -la $HOME/.cache/buildx/
   # Should be owned by your user
   ```

2. **Verify cache usage**:
   ```bash
   make debug-cache
   ```

3. **Clean and rebuild cache**:
   ```bash
   rm -rf $HOME/.cache/buildx/*
   # Next build will recreate cache
   ```

4. **Check disk space**:
   ```bash
   df -h $HOME/.cache
   ```

### Common Issues:

- **Permission denied**: Cache directories owned by root
- **No space left**: Disk full, clean old Docker images
- **Cache not found**: First build after cache cleanup
- **Network issues**: Registry cache unavailable

## Monitoring

### Build Time Tracking:

Add timing to your builds:
```bash
time docker buildx build ...
```

### Cache Effectiveness:

Monitor cache hit rates in build logs:
- Look for "CACHED" vs "RUN" in build output
- Registry cache shows "CACHED [internal]"
- Local cache shows faster layer pulls

## Next Steps

1. **Deploy the changes** and monitor first few builds
2. **Run debug script** after first build to verify cache creation
3. **Compare build times** before and after optimization
4. **Fine-tune cache strategies** based on your specific patterns

## Advanced Optimizations (Future)

1. **Parallel builds**: Build services in parallel with shared cache
2. **Multi-stage optimization**: Further optimize Dockerfile layers
3. **Registry cleanup**: Periodic cleanup of old cache images
4. **Build matrix**: Different cache strategies for different scenarios

The implemented changes should significantly improve your GitHub Actions build performance by ensuring proper cache utilization and persistence.
