name: CI - Deploy Dev Cluster

on:
  push:
    branches: [dev]

jobs:
  build-and-deploy:
    runs-on: wardaserver

    env:
      IMAGE_PREFIX: warda
      REGISTRY: ghcr.io/sietse-doubleprecision

    steps:
      - name: Set KUBECONFIG env
        run: echo "KUBECONFIG=/home/<USER>/.kube/config" >> $GITHUB_ENV

      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: docker/setup-buildx-action@v3.11.1
        with:
          driver-opts: |
            image=moby/buildkit:latest
            network=host

      - name: Setup persistent cache directories
        run: |
          # Use user-accessible persistent cache directories
          mkdir -p $HOME/.cache/buildx/{migrate,backend,frontend}
          mkdir -p $HOME/.cache/docker-buildx/{migrate,backend,frontend}

          # Create symlinks for easier access
          ln -sf $HOME/.cache/buildx $HOME/buildx-cache || true

      - name: Build images with optimized caching strategy
        run: |
          echo "🚀 Building all images with persistent caching..."

          # Build migrate with registry and local cache
          echo "📦 Building migrate..."
          docker buildx build \
            --file apps/migrate/Dockerfile \
            --tag $IMAGE_PREFIX/migrate:dev \
            --cache-from type=local,src=$HOME/.cache/buildx/migrate \
            --cache-from type=registry,ref=$REGISTRY/warda/migrate:cache \
            --cache-to type=local,dest=$HOME/.cache/buildx/migrate,mode=max \
            --cache-to type=registry,ref=$REGISTRY/warda/migrate:cache,mode=max \
            --load \
            .
          echo "✅ Migrate build complete"

          echo "📦 Building backend..."
          docker buildx build \
            --file apps/backend/Dockerfile \
            --tag $IMAGE_PREFIX/backend:dev \
            --cache-from type=local,src=$HOME/.cache/buildx/backend \
            --cache-from type=registry,ref=$REGISTRY/warda/backend:cache \
            --cache-to type=local,dest=$HOME/.cache/buildx/backend,mode=max \
            --cache-to type=registry,ref=$REGISTRY/warda/backend:cache,mode=max \
            --load \
            .
          echo "✅ Backend build complete"

          echo "📦 Building frontend..."
          docker buildx build \
            --file apps/frontend/Dockerfile \
            --tag $IMAGE_PREFIX/frontend:dev \
            --cache-from type=local,src=$HOME/.cache/buildx/frontend \
            --cache-from type=registry,ref=$REGISTRY/warda/frontend:cache \
            --cache-to type=local,dest=$HOME/.cache/buildx/frontend,mode=max \
            --cache-to type=registry,ref=$REGISTRY/warda/frontend:cache,mode=max \
            --load \
            .
          echo "✅ Frontend build complete"

          echo "🎉 All optimized builds completed!"

      - name: Import images to k3s in parallel
        run: |
          echo "📦 Importing images to k3s in parallel..."

          # Import all images in parallel for faster deployment
          (docker save $IMAGE_PREFIX/migrate:dev | sudo k3s ctr images import -) &
          (docker save $IMAGE_PREFIX/backend:dev | sudo k3s ctr images import -) &
          (docker save $IMAGE_PREFIX/frontend:dev | sudo k3s ctr images import -) &

          # Wait for all imports to complete
          wait

          # Verify images are available in k3s
          echo "✅ Images imported to k3s:"
          sudo k3s ctr images list | grep warda

      - uses: azure/setup-helm@v4.3.0
        with:
          version: 'v3.18.4'

      - name: Helm deploy with optimized strategy
        run: |
          echo "🚀 Deploying with optimized Helm strategy..."

          # Deploy with faster rollout strategy
          helm upgrade --install warda helm/warda \
            --namespace warda \
            --create-namespace \
            --set global.clusterHost=************* \
            --set global.image.registry="" \
            --set global.image.repository=warda \
            --set global.image.tag=dev \
            --set global.image.pullPolicy=Never \
            --set backend.strategy.type=RollingUpdate \
            --set backend.strategy.rollingUpdate.maxUnavailable=0 \
            --set backend.strategy.rollingUpdate.maxSurge=1 \
            --set frontend.strategy.type=RollingUpdate \
            --set frontend.strategy.rollingUpdate.maxUnavailable=0 \
            --set frontend.strategy.rollingUpdate.maxSurge=1 \
            --timeout=300s \
            --wait
