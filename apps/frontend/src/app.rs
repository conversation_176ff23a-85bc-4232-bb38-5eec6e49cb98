use walkers::{HttpTiles, sources::OpenStreetMap, sources::Mapbox};
use egui::{Context, CentralPanel};
use eframe::{App, Frame};

use crate::shared_data::SharedData;
use crate::theme::{ThemeManager, ThemePreference};
use crate::views::{Tab, show_counter_view, show_text_editor_view, show_data_list_view, show_map_view};


/// Main application with tab-based UI
#[derive(serde::Deserialize, serde::Serialize)]
#[serde(default)]
pub struct WardaFrontend {
    pub shared_data: SharedData,
    pub current_tab: Tab,

    // UI state
    pub show_preferences: bool,

    // Theme manager (partially persisted)
    #[serde(flatten)]
    pub theme_manager: ThemeManager,
}

impl Default for WardaFrontend {
    fn default() -> Self {
        Self {
            shared_data: SharedData::default(),
            current_tab: Tab::default(),
            show_preferences: false,
            theme_manager: ThemeManager::default(),
        }
    }
}

impl WardaFrontend {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        log::info!("Initializing Warda Frontend");

        let mut app: Self = if let Some(storage) = cc.storage {
            log::debug!("Loading app state from storage");
            let mut loaded_app: Self = eframe::get_value(storage, eframe::APP_KEY).unwrap_or_default();

            // For native builds, validate stored authentication state
            #[cfg(not(target_arch = "wasm32"))]
            {
                if loaded_app.shared_data.auth_state.is_authenticated {
                    log::info!("Found stored authentication state, will validate on startup");
                    let stored_auth_state = loaded_app.shared_data.auth_state.clone();
                    loaded_app.shared_data.prepare_auth_validation(stored_auth_state);
                }
            }

            loaded_app
        } else {
            log::debug!("No storage available, using default state");
            Default::default()
        };

        // Initialize map tiles
        app.initialize_map_tiles(cc);

        // Apply the stored theme preference immediately to the context
        app.theme_manager.initialize_theme(&cc.egui_ctx);

        // Initialize authentication state
        app.shared_data.update_auth_state();

        log::debug!("Warda Frontend initialized successfully");

        app
    }

    fn initialize_map_tiles(&mut self, cc: &eframe::CreationContext<'_>) {
        // Try to get Mapbox token from environment, fallback to OpenStreetMap
        log::info!("mapbox access token: {:#?}", std::env::var("MAPBOX_ACCESS_TOKEN"));
        let tiles = if let Ok(token) = std::env::var("MAPBOX_ACCESS_TOKEN") {
            if !token.is_empty() {
                log::info!("Using Mapbox tiles with provided token");
                HttpTiles::new(Mapbox { style: Default::default(), high_resolution: false, access_token: token }, cc.egui_ctx.clone())
            } else {
                log::warn!("MAPBOX_ACCESS_TOKEN is empty, falling back to OpenStreetMap");
                HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
            }
        } else {
            log::info!("No MAPBOX_ACCESS_TOKEN found, using OpenStreetMap");
            HttpTiles::new(OpenStreetMap, cc.egui_ctx.clone())
        };

        self.shared_data.tiles = Some(tiles);
    }
}

impl App for WardaFrontend {
    fn update(&mut self, ctx: &Context, _frame: &mut Frame) {
        // Apply theme preference if it has changed
        self.theme_manager.apply_theme_if_needed(ctx);

        // Update authentication state periodically
        #[cfg(target_arch = "wasm32")]
        {
            // Update auth state every few frames to keep it current
            static mut FRAME_COUNT: u32 = 0;
            unsafe {
                FRAME_COUNT += 1;
                if FRAME_COUNT % 60 == 0 { // Update every ~1 second at 60fps
                    self.shared_data.update_auth_state();
                }
            }
        }

        // For native builds, check authentication results every frame
        #[cfg(not(target_arch = "wasm32"))]
        {
            self.shared_data.update_auth_state();
        }

        // Log tab switches
        static mut LAST_TAB: Option<Tab> = None;
        unsafe {
            if LAST_TAB != Some(self.current_tab) {
                log::debug!("Switched to tab: {}", self.current_tab.name());
                LAST_TAB = Some(self.current_tab);
            }
        }

        let is_web = cfg!(target_arch = "wasm32");

        // Authentication status
        if self.shared_data.auth_state.is_authenticated {
            self.show_authenticated_ui(ctx, is_web);
        } else {
            // Show login screen when not authenticated
            CentralPanel::default().show(ctx, |ui| {
                self.show_login_screen(ui);
            });
        }
    }

    fn save(&mut self, storage: &mut dyn eframe::Storage) {
        log::debug!("Saving app state to storage");
        eframe::set_value(storage, eframe::APP_KEY, self);
    }
}

impl WardaFrontend {
    fn show_authenticated_ui(&mut self, ctx: &Context, is_web: bool) {
        egui::TopBottomPanel::top("top_panel").show(ctx, |ui| {
            egui::MenuBar::new().ui(ui, |ui| {
                ui.menu_button("File", |ui| {
                    if ui.button("Preferences").clicked() {
                        self.show_preferences = true;
                        ui.close();
                    }
                    if ui.button("Logout").clicked() {
                        log::info!("User requested logout");
                        self.shared_data.logout();
                    }
                    if !is_web {
                        if ui.button("Quit").clicked() {
                            log::info!("User requested quit");
                            ctx.send_viewport_cmd(egui::ViewportCommand::Close);
                        }
                    }
                });
                ui.menu_button("Edit", |ui| {
                    if ui.button("Copy").clicked() {

                    }
                    if ui.button("Paste").clicked() {

                    }
                });
                ui.add_space(16.0);
                ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
                    // Show logout button
                    if ui.button("🚪 Logout").clicked() {
                        log::info!("User clicked logout");
                        self.shared_data.logout();
                    }

                    // Show username or email
                    let display_name = self.shared_data.auth_state.username
                        .as_ref()
                        .or(self.shared_data.auth_state.email.as_ref())
                        .map(|s| s.as_str())
                        .unwrap_or("");

                    ui.label(format!("👤 {}", display_name));
                });
                // Show egui's built-in theme preference buttons for debugging
                egui::widgets::global_theme_preference_buttons(ui);
            });
        });

        // Tab bar
        egui::TopBottomPanel::top("tab_bar").show(ctx, |ui| {
            ui.horizontal(|ui| {
                ui.spacing_mut().item_spacing.x = 0.0;

                for tab in [Tab::Ingest, Tab::Verify, Tab::Enrich, Tab::Link] {
                    let selected = self.current_tab == tab;
                    if ui.selectable_label(selected, tab.name()).clicked() {
                        log::debug!("User clicked tab: {}", tab.name());
                        self.current_tab = tab;
                    }
                }
            });
        });

        // Main content area
        CentralPanel::default().show(ctx, |ui| {
            match self.current_tab {
                Tab::Ingest => show_counter_view(ui, &mut self.shared_data),
                Tab::Verify => show_text_editor_view(ui, &mut self.shared_data),
                Tab::Enrich => show_data_list_view(ui, &mut self.shared_data),
                Tab::Link => show_map_view(ui, &mut self.shared_data),
            }
        });

        // Show preferences window if requested
        if self.show_preferences {
            self.show_preferences_window(ctx);
        }
    }

    fn show_preferences_window(&mut self, ctx: &Context) {
        let mut theme_changed = false;
        let mut new_theme = self.theme_manager.theme_preference;

        egui::Window::new("Preferences")
            .open(&mut self.show_preferences)
            .show(ctx, |ui| {
                ui.horizontal(|ui| {
                    ui.label("Theme:");
                    egui::ComboBox::from_label("")
                        .selected_text(new_theme.display_name())
                        .show_ui(ui, |ui| {
                            for &theme in ThemePreference::all() {
                                if ui.selectable_value(&mut new_theme, theme, theme.display_name()).clicked() {
                                    theme_changed = true;
                                }
                            }
                        });
                });
            });

        // Apply theme changes outside the closure to avoid borrowing issues
        if theme_changed {
            self.theme_manager.set_theme_preference(new_theme, ctx);
        }
    }

    fn show_login_screen(&mut self, ui: &mut egui::Ui) {
        ui.vertical_centered(|ui| {
            ui.add_space(100.0);

            // App logo/title
            ui.heading("🗺️ Warda");
            ui.add_space(20.0);

            ui.label("Welcome to Warda - Interactive Map Application");
            ui.add_space(40.0);

            // Authentication status
            #[cfg(not(target_arch = "wasm32"))]
            {
                if self.shared_data.is_login_in_progress() {
                    ui.add_space(20.0);
                    ui.label("🔄 Authenticating...");
                    ui.add_space(10.0);
                    ui.label("Please complete the login in your browser.");
                    ui.add_space(10.0);
                    ui.label("This window will update automatically once you're authenticated.");
                    ui.add_space(20.0);

                    // Add cancel button
                    ui.horizontal(|ui| {
                        ui.add_space(50.0); // Center the button
                        if ui.button("❌ Cancel Authentication").clicked() {
                            log::info!("User cancelled authentication");
                            self.shared_data.cancel_authentication();
                        }
                    });

                    ui.add_space(10.0);
                    ui.label("Click cancel if your browser crashed or closed.");
                } else {
                    ui.label("Please log in to access the application.");
                    ui.add_space(20.0);

                    if ui.button("🔐 Login with Keycloak").clicked() {
                        log::info!("User clicked login from login screen");
                        self.shared_data.start_native_login();
                    }

                    ui.add_space(20.0);
                    ui.label("This will open your browser to authenticate with Keycloak.");
                }
            }

            #[cfg(target_arch = "wasm32")]
            {
                ui.label("🔒 Authentication required");
                ui.add_space(10.0);
                ui.label("Please ensure you are logged in to access the application.");
            }
        });
    }
}