use walkers::{Map, lon_lat};
use crate::shared_data::SharedData;

pub fn show_map_view(ui: &mut egui::Ui, shared_data: &mut SharedData) {
    ui.horizontal(|ui| {
        ui.label("Counter:");
        ui.label(shared_data.counter.to_string());
        if ui.button("Add marker count").clicked() {
            shared_data.counter += 1;
            log::debug!("Map view: Counter incremented to {}", shared_data.counter);
        }
    });

    ui.separator();

    if let Some(ref mut tiles) = shared_data.tiles {
        ui.add(Map::new(
            Some(tiles),
            &mut shared_data.map_memory,
            lon_lat(10.7522, 59.9139)
        ));
    } else {
        log::error!("Map tiles not initialized");
        ui.label("Map tiles not initialized");
    }
}