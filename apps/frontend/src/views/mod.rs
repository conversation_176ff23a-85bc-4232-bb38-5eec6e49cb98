pub mod ingest;
pub mod text_editor;
pub mod data_list;
pub mod map;

pub use ingest::show_counter_view;
pub use text_editor::show_text_editor_view;
pub use data_list::show_data_list_view;
pub use map::show_map_view;

#[cfg(target_arch = "wasm32")]
use web_sys;

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, serde::Deserialize, serde::Serialize)]
pub enum Tab {
    Ingest,
    Verify,
    Enrich,
    Link,
}

impl Default for Tab {
    fn default() -> Self {
        Tab::Ingest
    }
}

impl Tab {
    pub fn name(&self) -> String {
        match self {
            Tab::Ingest => format!("{} Ingest", '\u{1F4E9}'),
            Tab::Verify => format!("{} Verify", '\u{2714}'),
            Tab::Enrich => format!("{} Enrich", '\u{1F50D}'),
            Tab::Link => format!("{} Link", '\u{1F578}'),
        }
    }
}