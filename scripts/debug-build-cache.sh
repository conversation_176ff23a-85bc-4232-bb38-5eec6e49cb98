#!/bin/bash

# Debug script for analyzing Docker build cache performance
# Usage: ./scripts/debug-build-cache.sh

set -e

echo "🔍 Docker Build Cache Debug Report"
echo "=================================="
echo

# Check Docker and BuildKit versions
echo "📋 System Information:"
echo "Docker version: $(docker --version)"
echo "BuildKit version: $(docker buildx version)"
echo "Available builders:"
docker buildx ls
echo

# Check cache directories
echo "📁 Cache Directory Status:"
echo "Local cache directories:"
if [ -d "/var/cache/buildx" ]; then
    echo "  /var/cache/buildx: $(du -sh /var/cache/buildx 2>/dev/null || echo 'Permission denied')"
    ls -la /var/cache/buildx/ 2>/dev/null || echo "  Cannot list contents"
else
    echo "  /var/cache/buildx: Not found"
fi

if [ -d "/tmp/.buildx-cache-migrate" ]; then
    echo "  /tmp/.buildx-cache-migrate: $(du -sh /tmp/.buildx-cache-migrate)"
fi
if [ -d "/tmp/.buildx-cache-backend" ]; then
    echo "  /tmp/.buildx-cache-backend: $(du -sh /tmp/.buildx-cache-backend)"
fi
if [ -d "/tmp/.buildx-cache-frontend" ]; then
    echo "  /tmp/.buildx-cache-frontend: $(du -sh /tmp/.buildx-cache-frontend)"
fi

echo "User cache directories:"
if [ -d "$HOME/.cache/buildx" ]; then
    echo "  $HOME/.cache/buildx: $(du -sh $HOME/.cache/buildx)"
    ls -la $HOME/.cache/buildx/ 2>/dev/null || echo "  Cannot list contents"
else
    echo "  $HOME/.cache/buildx: Not found"
fi
echo

# Check Docker system info
echo "🐳 Docker System Information:"
echo "Docker system df:"
docker system df
echo
echo "BuildKit cache usage:"
docker buildx du 2>/dev/null || echo "BuildKit cache info not available"
echo

# Check for registry cache images
echo "🗄️ Registry Cache Images:"
REGISTRY="${REGISTRY:-ghcr.io/sietse-doubleprecision}"
echo "Checking for cache images in $REGISTRY..."

for service in migrate backend frontend; do
    echo "  Checking $service cache..."
    if docker manifest inspect "$REGISTRY/warda/$service:cache" >/dev/null 2>&1; then
        echo "    ✅ Cache image exists for $service"
        # Get image size
        docker manifest inspect "$REGISTRY/warda/$service:cache" | jq -r '.config.size // "Unknown"' 2>/dev/null || echo "    Size: Unknown"
    else
        echo "    ❌ No cache image found for $service"
    fi
done
echo

# Test build performance
echo "🚀 Build Performance Test:"
echo "Testing build time for migrate service (with cache)..."

# Clean build (no cache)
echo "1. Clean build (no cache):"
time_start=$(date +%s)
docker buildx build \
    --file apps/migrate/Dockerfile \
    --tag test-migrate:clean \
    --no-cache \
    --load \
    . >/dev/null 2>&1
time_end=$(date +%s)
clean_time=$((time_end - time_start))
echo "   Clean build time: ${clean_time}s"

# Cached build
echo "2. Cached build:"
time_start=$(date +%s)
docker buildx build \
    --file apps/migrate/Dockerfile \
    --tag test-migrate:cached \
    --cache-from type=local,src=/var/cache/buildx/migrate \
    --load \
    . >/dev/null 2>&1
time_end=$(date +%s)
cached_time=$((time_end - time_start))
echo "   Cached build time: ${cached_time}s"

# Calculate improvement
if [ $clean_time -gt 0 ]; then
    improvement=$(( (clean_time - cached_time) * 100 / clean_time ))
    echo "   Cache improvement: ${improvement}% faster"
fi

# Cleanup test images
docker rmi test-migrate:clean test-migrate:cached >/dev/null 2>&1 || true
echo

# Check for common issues
echo "⚠️  Common Issues Check:"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "  ❌ Running as root - this can cause cache permission issues"
else
    echo "  ✅ Not running as root"
fi

# Check disk space
available_space=$(df /var/cache 2>/dev/null | tail -1 | awk '{print $4}' || echo "0")
if [ "$available_space" -lt 1048576 ]; then  # Less than 1GB
    echo "  ⚠️  Low disk space in /var/cache: $(df -h /var/cache 2>/dev/null | tail -1 | awk '{print $4}' || echo 'Unknown')"
else
    echo "  ✅ Sufficient disk space available"
fi

# Check BuildKit features
echo "  BuildKit features:"
if docker buildx inspect --bootstrap >/dev/null 2>&1; then
    docker buildx inspect | grep -E "(Driver|Platforms|Features)" || echo "    Cannot inspect builder"
else
    echo "    ❌ Cannot inspect BuildKit builder"
fi

echo
echo "🎯 Recommendations:"
echo "1. Ensure cache directories have proper permissions"
echo "2. Use persistent cache locations (/var/cache/buildx instead of /tmp)"
echo "3. Consider using registry cache for better persistence"
echo "4. Monitor cache hit rates during builds"
echo "5. Clean old cache periodically: docker buildx prune"
echo
echo "Debug report complete!"
